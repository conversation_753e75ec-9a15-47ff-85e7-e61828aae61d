#!/usr/bin/env node

/**
 * Firebase Setup Script
 * This script helps set up Firebase configuration for the Dating App
 */

const fs = require('fs');
const path = require('path');

const FIREBASE_CONFIG_PATH = path.join(__dirname, '..', 'ios', 'DatingApp', 'GoogleService-Info.plist');
const TEMPLATE_PATH = path.join(__dirname, '..', 'ios', 'GoogleService-Info-TEMPLATE.plist');

function checkFirebaseConfig() {
  console.log('🔥 Firebase Setup Checker\n');

  // Check if GoogleService-Info.plist exists
  if (fs.existsSync(FIREBASE_CONFIG_PATH)) {
    console.log('✅ GoogleService-Info.plist found');
    
    // Read and validate the file
    try {
      const content = fs.readFileSync(FIREBASE_CONFIG_PATH, 'utf8');
      
      if (content.includes('YOUR_API_KEY_HERE') || content.includes('YOUR_PROJECT_ID_HERE')) {
        console.log('⚠️  GoogleService-Info.plist contains template values');
        console.log('   Please download the real file from Firebase Console');
        return false;
      }
      
      console.log('✅ GoogleService-Info.plist appears to be configured');
      return true;
    } catch (error) {
      console.log('❌ Error reading GoogleService-Info.plist:', error.message);
      return false;
    }
  } else {
    console.log('❌ GoogleService-Info.plist not found');
    console.log('   Expected location:', FIREBASE_CONFIG_PATH);
    
    if (fs.existsSync(TEMPLATE_PATH)) {
      console.log('📋 Template file found at:', TEMPLATE_PATH);
    }
    
    return false;
  }
}

function printSetupInstructions() {
  console.log('\n📋 Firebase Setup Instructions:\n');
  
  console.log('1. Go to Firebase Console: https://console.firebase.google.com/');
  console.log('2. Create a new project or select existing project');
  console.log('3. Add iOS app with bundle ID: org.reactjs.native.example.DatingApp');
  console.log('4. Download GoogleService-Info.plist');
  console.log('5. Place the file at: ios/DatingApp/GoogleService-Info.plist');
  console.log('6. In Xcode, add the file to the DatingApp target');
  console.log('7. Enable these Firebase services:');
  console.log('   - Authentication (Email/Password)');
  console.log('   - Firestore Database');
  console.log('   - Storage');
  console.log('   - Cloud Messaging');
  
  console.log('\n🔐 Security Rules:');
  console.log('Copy the security rules from FIREBASE_SETUP.md to your Firebase Console');
  
  console.log('\n🧪 Testing:');
  console.log('Run: npm test');
  console.log('Run: npx react-native run-ios');
}

function main() {
  const isConfigured = checkFirebaseConfig();
  
  if (!isConfigured) {
    printSetupInstructions();
    process.exit(1);
  } else {
    console.log('\n🎉 Firebase appears to be configured correctly!');
    console.log('You can now run: npx react-native run-ios');
  }
}

if (require.main === module) {
  main();
}

module.exports = {
  checkFirebaseConfig,
  printSetupInstructions
};
