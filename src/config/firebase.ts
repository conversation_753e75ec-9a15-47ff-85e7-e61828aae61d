/**
 * Firebase Configuration
 */

import {FirebaseApp, getApps, initializeApp} from '@react-native-firebase/app';
import auth, {FirebaseAuthTypes} from '@react-native-firebase/auth';
import firestore, {FirebaseFirestoreTypes} from '@react-native-firebase/firestore';
import storage, {FirebaseStorageTypes} from '@react-native-firebase/storage';
import messaging, {FirebaseMessagingTypes} from '@react-native-firebase/messaging';

// Firebase configuration (these will be read from GoogleService-Info.plist)
const firebaseConfig = {
  // Configuration is automatically loaded from GoogleService-Info.plist on iOS
  // and google-services.json on Android
};

// Initialize Firebase if not already initialized
let app: FirebaseApp;
if (getApps().length === 0) {
  app = initializeApp(firebaseConfig);
} else {
  app = getApps()[0];
}

// Export Firebase services
export const firebaseAuth = auth();
export const firebaseFirestore = firestore();
export const firebaseStorage = storage();
export const firebaseMessaging = messaging();

// Export types for TypeScript
export type {
  FirebaseAuthTypes,
  FirebaseFirestoreTypes,
  FirebaseStorageTypes,
  FirebaseMessagingTypes,
};

// Collection names
export const COLLECTIONS = {
  USERS: 'users',
  PROFILES: 'profiles',
  MATCHES: 'matches',
  CHATS: 'chats',
  MESSAGES: 'messages',
  REPORTS: 'reports',
  SUBSCRIPTIONS: 'subscriptions',
} as const;

// Storage paths
export const STORAGE_PATHS = {
  PROFILE_PHOTOS: 'profile-photos',
  CHAT_IMAGES: 'chat-images',
  VERIFICATION_PHOTOS: 'verification-photos',
} as const;

export default app;
