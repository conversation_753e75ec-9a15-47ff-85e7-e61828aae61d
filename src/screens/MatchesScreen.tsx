import React, {useState} from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  Image,
  RefreshControl,
} from 'react-native';
import {SafeAreaView} from 'react-native-safe-area-context';
import Icon from 'react-native-vector-icons/Ionicons';
import {useNavigation} from '@react-navigation/native';
import {useChat} from '../context/ChatContext';
import {Match} from '../types/chat';
import {formatDistance, isUserOnline} from '../utils/helpers';

export default function MatchesScreen() {
  const [refreshing, setRefreshing] = useState(false);
  const navigation = useNavigation();
  const {state} = useChat();

  const onRefresh = async () => {
    setRefreshing(true);
    // In real app, this would refresh matches
    setTimeout(() => setRefreshing(false), 1000);
  };

  const navigateToChat = (match: Match) => {
    navigation.navigate('Chat' as never, {
      matchId: match.id,
      matchName: match.name,
      matchPhoto: match.photos[0],
    } as never);
  };

  const getLastMessagePreview = (match: Match) => {
    if (!match.chat?.lastMessage) {
      return 'Say hello! 👋';
    }

    const message = match.chat.lastMessage;
    if (message.type === 'image') return '📷 Photo';
    if (message.type === 'gif') return '🎬 GIF';
    if (message.type === 'emoji') return message.content;

    return message.content.length > 50
      ? `${message.content.substring(0, 50)}...`
      : message.content;
  };

  const getTimeAgo = (date: Date) => {
    const now = new Date();
    const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));

    if (diffInMinutes < 1) return 'now';
    if (diffInMinutes < 60) return `${diffInMinutes}m`;
    if (diffInMinutes < 1440) return `${Math.floor(diffInMinutes / 60)}h`;
    return `${Math.floor(diffInMinutes / 1440)}d`;
  };

  const renderMatchItem = ({item}: {item: Match}) => (
    <TouchableOpacity style={styles.matchItem} onPress={() => navigateToChat(item)}>
      <View style={styles.matchImageContainer}>
        <Image source={{uri: item.photos[0]}} style={styles.matchImage} />
        {item.isOnline && <View style={styles.onlineIndicator} />}
        {item.chat && item.chat.unreadCount > 0 && (
          <View style={styles.unreadBadge}>
            <Text style={styles.unreadCount}>
              {item.chat.unreadCount > 9 ? '9+' : item.chat.unreadCount}
            </Text>
          </View>
        )}
      </View>

      <View style={styles.matchInfo}>
        <View style={styles.matchHeader}>
          <Text style={styles.matchName}>{item.name}</Text>
          <Text style={styles.matchTime}>
            {item.chat?.lastMessage
              ? getTimeAgo(item.chat.lastMessage.timestamp)
              : getTimeAgo(item.matchDate)
            }
          </Text>
        </View>

        <Text style={styles.matchMessage}>
          {getLastMessagePreview(item)}
        </Text>

        <View style={styles.matchDetails}>
          <Text style={styles.matchAge}>{item.age} • {formatDistance(item.distance)}</Text>
          {item.commonInterests.length > 0 && (
            <Text style={styles.commonInterests}>
              {item.commonInterests.slice(0, 2).join(', ')}
            </Text>
          )}
        </View>
      </View>

      <Icon name="chevron-forward" size={20} color="#999" />
    </TouchableOpacity>
  );

  const renderEmptyState = () => (
    <View style={styles.emptyState}>
      <Icon name="heart-outline" size={80} color="#DDD" />
      <Text style={styles.emptyTitle}>No Matches Yet</Text>
      <Text style={styles.emptySubtitle}>
        Keep swiping to find your perfect match!
      </Text>
      <TouchableOpacity style={styles.discoverButton}>
        <Text style={styles.discoverButtonText}>Start Discovering</Text>
      </TouchableOpacity>
    </View>
  );

  if (state.isLoading) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.header}>
          <Text style={styles.headerTitle}>Matches</Text>
        </View>
        <View style={styles.loadingContainer}>
          <Text style={styles.loadingText}>Loading matches...</Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.headerTitle}>Matches</Text>
        <TouchableOpacity style={styles.filterButton}>
          <Icon name="options-outline" size={24} color="#FF6B6B" />
        </TouchableOpacity>
      </View>

      {state.matches.length === 0 ? (
        renderEmptyState()
      ) : (
        <FlatList
          data={state.matches}
          renderItem={renderMatchItem}
          keyExtractor={(item) => item.id}
          refreshControl={
            <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
          }
          showsVerticalScrollIndicator={false}
          contentContainerStyle={styles.listContainer}
        />
      )}
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#F0F0F0',
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#333',
  },
  filterButton: {
    padding: 8,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    fontSize: 16,
    color: '#666',
  },
  listContainer: {
    paddingVertical: 8,
  },
  matchItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#F8F8F8',
  },
  matchImageContainer: {
    position: 'relative',
    marginRight: 16,
  },
  matchImage: {
    width: 60,
    height: 60,
    borderRadius: 30,
  },
  onlineIndicator: {
    position: 'absolute',
    bottom: 2,
    right: 2,
    width: 16,
    height: 16,
    borderRadius: 8,
    backgroundColor: '#4CAF50',
    borderWidth: 2,
    borderColor: '#fff',
  },
  unreadBadge: {
    position: 'absolute',
    top: -4,
    right: -4,
    backgroundColor: '#FF6B6B',
    borderRadius: 10,
    minWidth: 20,
    height: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  unreadCount: {
    color: '#fff',
    fontSize: 12,
    fontWeight: 'bold',
  },
  matchInfo: {
    flex: 1,
    marginRight: 12,
  },
  matchHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 4,
  },
  matchName: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
  },
  matchTime: {
    fontSize: 12,
    color: '#999',
  },
  matchMessage: {
    fontSize: 14,
    color: '#666',
    marginBottom: 4,
  },
  matchDetails: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  matchAge: {
    fontSize: 12,
    color: '#999',
  },
  commonInterests: {
    fontSize: 12,
    color: '#FF6B6B',
    fontWeight: '500',
  },
  emptyState: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 40,
  },
  emptyTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#333',
    marginTop: 20,
    marginBottom: 8,
  },
  emptySubtitle: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
    lineHeight: 22,
    marginBottom: 32,
  },
  discoverButton: {
    backgroundColor: '#FF6B6B',
    paddingHorizontal: 32,
    paddingVertical: 12,
    borderRadius: 25,
  },
  discoverButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
  },
});
