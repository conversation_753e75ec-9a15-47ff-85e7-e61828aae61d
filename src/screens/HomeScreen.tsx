import React, {useState} from 'react';
import {
  View,
  Text,
  StyleSheet,
  Dimensions,
  Image,
  TouchableOpacity,
  PanResponder,
  Animated,
} from 'react-native';
import {SafeAreaView} from 'react-native-safe-area-context';
import Icon from 'react-native-vector-icons/Ionicons';

const {width: screenWidth, height: screenHeight} = Dimensions.get('window');

// Mock user data
const mockUsers = [
  {
    id: 1,
    name: '<PERSON>',
    age: 25,
    bio: 'Love hiking and coffee ☕',
    image: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=400',
  },
  {
    id: 2,
    name: '<PERSON>',
    age: 28,
    bio: 'Yoga instructor & dog lover 🐕',
    image: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=400',
  },
  {
    id: 3,
    name: 'Jessica',
    age: 26,
    bio: 'Artist and traveler ✈️',
    image: 'https://images.unsplash.com/photo-1544005313-94ddf0286df2?w=400',
  },
];

export default function HomeScreen() {
  const [currentIndex, setCurrentIndex] = useState(0);
  const [cards] = useState(mockUsers);
  const position = new Animated.ValueXY();

  const panResponder = PanResponder.create({
    onStartShouldSetPanResponder: () => true,
    onMoveShouldSetPanResponder: () => true,
    onPanResponderMove: (evt, gestureState) => {
      position.setValue({x: gestureState.dx, y: gestureState.dy});
    },
    onPanResponderRelease: (evt, gestureState) => {
      if (gestureState.dx > 120) {
        // Swipe right - Like
        handleLike();
      } else if (gestureState.dx < -120) {
        // Swipe left - Pass
        handlePass();
      } else {
        // Return to center
        Animated.spring(position, {
          toValue: {x: 0, y: 0},
          useNativeDriver: false,
        }).start();
      }
    },
  });

  const handleLike = () => {
    Animated.timing(position, {
      toValue: {x: screenWidth + 100, y: 0},
      duration: 300,
      useNativeDriver: false,
    }).start(() => {
      nextCard();
    });
  };

  const handlePass = () => {
    Animated.timing(position, {
      toValue: {x: -screenWidth - 100, y: 0},
      duration: 300,
      useNativeDriver: false,
    }).start(() => {
      nextCard();
    });
  };

  const nextCard = () => {
    setCurrentIndex(currentIndex + 1);
    position.setValue({x: 0, y: 0});
  };

  const renderCard = (user: any, index: number) => {
    if (index < currentIndex) {
      return null;
    }

    if (index === currentIndex) {
      const rotate = position.x.interpolate({
        inputRange: [-screenWidth / 2, 0, screenWidth / 2],
        outputRange: ['-30deg', '0deg', '30deg'],
        extrapolate: 'clamp',
      });

      const likeOpacity = position.x.interpolate({
        inputRange: [0, 150],
        outputRange: [0, 1],
        extrapolate: 'clamp',
      });

      const passOpacity = position.x.interpolate({
        inputRange: [-150, 0],
        outputRange: [1, 0],
        extrapolate: 'clamp',
      });

      return (
        <Animated.View
          key={user.id}
          style={[
            styles.card,
            {
              transform: [{translateX: position.x}, {translateY: position.y}, {rotate}],
            },
          ]}
          {...panResponder.panHandlers}>
          <Image source={{uri: user.image}} style={styles.cardImage} />
          <View style={styles.cardInfo}>
            <Text style={styles.cardName}>
              {user.name}, {user.age}
            </Text>
            <Text style={styles.cardBio}>{user.bio}</Text>
          </View>
          
          {/* Like/Pass overlays */}
          <Animated.View style={[styles.likeOverlay, {opacity: likeOpacity}]}>
            <Text style={styles.likeText}>LIKE</Text>
          </Animated.View>
          <Animated.View style={[styles.passOverlay, {opacity: passOpacity}]}>
            <Text style={styles.passText}>PASS</Text>
          </Animated.View>
        </Animated.View>
      );
    }

    return (
      <View key={user.id} style={[styles.card, styles.nextCard]}>
        <Image source={{uri: user.image}} style={styles.cardImage} />
        <View style={styles.cardInfo}>
          <Text style={styles.cardName}>
            {user.name}, {user.age}
          </Text>
          <Text style={styles.cardBio}>{user.bio}</Text>
        </View>
      </View>
    );
  };

  if (currentIndex >= cards.length) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.noMoreCards}>
          <Icon name="heart-outline" size={80} color="#FF6B6B" />
          <Text style={styles.noMoreText}>No more profiles!</Text>
          <Text style={styles.noMoreSubtext}>Check back later for new matches</Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.headerTitle}>Discover</Text>
      </View>
      
      <View style={styles.cardContainer}>
        {cards.map((user, index) => renderCard(user, index)).reverse()}
      </View>

      <View style={styles.actionButtons}>
        <TouchableOpacity style={styles.passButton} onPress={handlePass}>
          <Icon name="close" size={30} color="#FF4458" />
        </TouchableOpacity>
        <TouchableOpacity style={styles.likeButton} onPress={handleLike}>
          <Icon name="heart" size={30} color="#66D7D2" />
        </TouchableOpacity>
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f8f8',
  },
  header: {
    paddingHorizontal: 20,
    paddingVertical: 15,
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#333',
  },
  cardContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
  },
  card: {
    width: screenWidth * 0.9,
    height: screenHeight * 0.7,
    backgroundColor: 'white',
    borderRadius: 20,
    position: 'absolute',
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  nextCard: {
    transform: [{scale: 0.95}],
    opacity: 0.8,
  },
  cardImage: {
    width: '100%',
    height: '75%',
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
  },
  cardInfo: {
    padding: 20,
    flex: 1,
  },
  cardName: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 5,
  },
  cardBio: {
    fontSize: 16,
    color: '#666',
  },
  likeOverlay: {
    position: 'absolute',
    top: 50,
    left: 50,
    backgroundColor: '#66D7D2',
    padding: 10,
    borderRadius: 10,
    transform: [{rotate: '-30deg'}],
  },
  likeText: {
    color: 'white',
    fontSize: 20,
    fontWeight: 'bold',
  },
  passOverlay: {
    position: 'absolute',
    top: 50,
    right: 50,
    backgroundColor: '#FF4458',
    padding: 10,
    borderRadius: 10,
    transform: [{rotate: '30deg'}],
  },
  passText: {
    color: 'white',
    fontSize: 20,
    fontWeight: 'bold',
  },
  actionButtons: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    paddingHorizontal: 50,
    paddingBottom: 30,
  },
  passButton: {
    width: 60,
    height: 60,
    borderRadius: 30,
    backgroundColor: 'white',
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  likeButton: {
    width: 60,
    height: 60,
    borderRadius: 30,
    backgroundColor: 'white',
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  noMoreCards: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  noMoreText: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#333',
    marginTop: 20,
  },
  noMoreSubtext: {
    fontSize: 16,
    color: '#666',
    marginTop: 10,
  },
});
