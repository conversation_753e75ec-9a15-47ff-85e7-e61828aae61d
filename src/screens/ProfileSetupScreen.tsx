import React, {useState} from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  Alert,
  ScrollView,
  Image,
} from 'react-native';
import {SafeAreaView} from 'react-native-safe-area-context';
import Icon from 'react-native-vector-icons/Ionicons';
import {launchImageLibrary} from 'react-native-image-picker';
import {useAuth} from '../context/AuthContext';
import {UserProfile} from '../types/auth';

const INTERESTS = [
  'Travel', 'Music', 'Movies', 'Sports', 'Reading', 'Cooking',
  'Photography', 'Art', 'Dancing', 'Hiking', 'Gaming', 'Fitness',
  'Fashion', 'Technology', 'Food', 'Animals', 'Nature', 'Adventure'
];

const RELATIONSHIP_GOALS = [
  {id: 'casual', label: 'Casual Dating', icon: 'happy-outline'},
  {id: 'serious', label: 'Serious Relationship', icon: 'heart-outline'},
  {id: 'friendship', label: 'Friendship', icon: 'people-outline'},
  {id: 'unsure', label: 'Not Sure Yet', icon: 'help-outline'},
];

export default function ProfileSetupScreen() {
  const [currentStep, setCurrentStep] = useState(1);
  const [isLoading, setIsLoading] = useState(false);
  const [profileData, setProfileData] = useState<Partial<UserProfile>>({
    bio: '',
    photos: [],
    interests: [],
    location: {
      city: '',
      state: '',
      country: '',
    },
    preferences: {
      ageRange: {min: 18, max: 35},
      maxDistance: 50,
      genderPreference: 'everyone',
    },
    relationshipGoals: 'unsure',
  });

  const {updateProfile, state} = useAuth();

  const handleNext = () => {
    if (currentStep < 4) {
      setCurrentStep(currentStep + 1);
    } else {
      handleComplete();
    }
  };

  const handleBack = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    }
  };

  const handleComplete = async () => {
    setIsLoading(true);
    try {
      await updateProfile(profileData);
      // The profile completion will trigger navigation automatically
      // via the AppNavigator checking state.user?.profileComplete
      Alert.alert('Success', 'Profile setup completed!');
    } catch (error) {
      Alert.alert('Error', 'Failed to save profile. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const addPhoto = () => {
    launchImageLibrary(
      {
        mediaType: 'photo',
        quality: 0.8,
        maxWidth: 800,
        maxHeight: 800,
      },
      (response) => {
        if (response.assets && response.assets[0]) {
          const newPhoto = response.assets[0].uri || '';
          setProfileData(prev => ({
            ...prev,
            photos: [...(prev.photos || []), newPhoto],
          }));
        }
      }
    );
  };

  const removePhoto = (index: number) => {
    setProfileData(prev => ({
      ...prev,
      photos: prev.photos?.filter((_, i) => i !== index) || [],
    }));
  };

  const toggleInterest = (interest: string) => {
    setProfileData(prev => {
      const interests = prev.interests || [];
      const isSelected = interests.includes(interest);
      
      return {
        ...prev,
        interests: isSelected
          ? interests.filter(i => i !== interest)
          : [...interests, interest],
      };
    });
  };

  const renderStepIndicator = () => (
    <View style={styles.stepIndicator}>
      {[1, 2, 3, 4].map((step) => (
        <View
          key={step}
          style={[
            styles.stepDot,
            currentStep >= step && styles.stepDotActive,
          ]}
        />
      ))}
    </View>
  );

  const renderStep1 = () => (
    <View style={styles.stepContent}>
      <Text style={styles.stepTitle}>Add Your Photos</Text>
      <Text style={styles.stepSubtitle}>Upload at least 2 photos to get started</Text>
      
      <View style={styles.photosGrid}>
        {profileData.photos?.map((photo, index) => (
          <View key={index} style={styles.photoContainer}>
            <Image source={{uri: photo}} style={styles.photo} />
            <TouchableOpacity
              style={styles.removePhotoButton}
              onPress={() => removePhoto(index)}>
              <Icon name="close" size={16} color="#fff" />
            </TouchableOpacity>
          </View>
        ))}
        
        {(profileData.photos?.length || 0) < 6 && (
          <TouchableOpacity style={styles.addPhotoButton} onPress={addPhoto}>
            <Icon name="camera" size={30} color="#FF6B6B" />
            <Text style={styles.addPhotoText}>Add Photo</Text>
          </TouchableOpacity>
        )}
      </View>
    </View>
  );

  const renderStep2 = () => (
    <View style={styles.stepContent}>
      <Text style={styles.stepTitle}>Tell Us About Yourself</Text>
      <Text style={styles.stepSubtitle}>Write a bio that shows your personality</Text>
      
      <TextInput
        style={styles.bioInput}
        placeholder="Write something about yourself..."
        value={profileData.bio}
        onChangeText={(text) => setProfileData(prev => ({...prev, bio: text}))}
        multiline
        maxLength={500}
        textAlignVertical="top"
      />
      <Text style={styles.characterCount}>
        {profileData.bio?.length || 0}/500 characters
      </Text>
    </View>
  );

  const renderStep3 = () => (
    <View style={styles.stepContent}>
      <Text style={styles.stepTitle}>Your Interests</Text>
      <Text style={styles.stepSubtitle}>Select what you're passionate about</Text>
      
      <View style={styles.interestsGrid}>
        {INTERESTS.map((interest) => (
          <TouchableOpacity
            key={interest}
            style={[
              styles.interestChip,
              profileData.interests?.includes(interest) && styles.interestChipSelected,
            ]}
            onPress={() => toggleInterest(interest)}>
            <Text
              style={[
                styles.interestText,
                profileData.interests?.includes(interest) && styles.interestTextSelected,
              ]}>
              {interest}
            </Text>
          </TouchableOpacity>
        ))}
      </View>
    </View>
  );

  const renderStep4 = () => (
    <View style={styles.stepContent}>
      <Text style={styles.stepTitle}>What Are You Looking For?</Text>
      <Text style={styles.stepSubtitle}>Help us find your perfect match</Text>
      
      <View style={styles.goalsList}>
        {RELATIONSHIP_GOALS.map((goal) => (
          <TouchableOpacity
            key={goal.id}
            style={[
              styles.goalOption,
              profileData.relationshipGoals === goal.id && styles.goalOptionSelected,
            ]}
            onPress={() => setProfileData(prev => ({
              ...prev,
              relationshipGoals: goal.id as any,
            }))}>
            <Icon
              name={goal.icon}
              size={24}
              color={profileData.relationshipGoals === goal.id ? '#FF6B6B' : '#666'}
            />
            <Text
              style={[
                styles.goalText,
                profileData.relationshipGoals === goal.id && styles.goalTextSelected,
              ]}>
              {goal.label}
            </Text>
          </TouchableOpacity>
        ))}
      </View>
    </View>
  );

  const renderCurrentStep = () => {
    switch (currentStep) {
      case 1: return renderStep1();
      case 2: return renderStep2();
      case 3: return renderStep3();
      case 4: return renderStep4();
      default: return renderStep1();
    }
  };

  const canProceed = () => {
    switch (currentStep) {
      case 1: return (profileData.photos?.length || 0) >= 2;
      case 2: return (profileData.bio?.length || 0) >= 50;
      case 3: return (profileData.interests?.length || 0) >= 3;
      case 4: return profileData.relationshipGoals !== 'unsure';
      default: return false;
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        {currentStep > 1 && (
          <TouchableOpacity onPress={handleBack} style={styles.backButton}>
            <Icon name="arrow-back" size={24} color="#333" />
          </TouchableOpacity>
        )}
        <Text style={styles.headerTitle}>Setup Profile</Text>
        <Text style={styles.stepCounter}>{currentStep}/4</Text>
      </View>

      {renderStepIndicator()}

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {renderCurrentStep()}
      </ScrollView>

      <View style={styles.footer}>
        <TouchableOpacity
          style={[
            styles.nextButton,
            (!canProceed() || isLoading) && styles.nextButtonDisabled,
          ]}
          onPress={handleNext}
          disabled={!canProceed() || isLoading}>
          <Text style={styles.nextButtonText}>
            {isLoading ? 'Saving...' : (currentStep === 4 ? 'Complete' : 'Next')}
          </Text>
        </TouchableOpacity>
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#E0E0E0',
  },
  backButton: {
    padding: 8,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333',
  },
  stepCounter: {
    fontSize: 14,
    color: '#666',
  },
  stepIndicator: {
    flexDirection: 'row',
    justifyContent: 'center',
    paddingVertical: 20,
  },
  stepDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: '#E0E0E0',
    marginHorizontal: 4,
  },
  stepDotActive: {
    backgroundColor: '#FF6B6B',
  },
  content: {
    flex: 1,
    paddingHorizontal: 20,
  },
  stepContent: {
    paddingVertical: 20,
  },
  stepTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#333',
    textAlign: 'center',
    marginBottom: 8,
  },
  stepSubtitle: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
    marginBottom: 32,
  },
  photosGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  photoContainer: {
    width: '48%',
    aspectRatio: 1,
    marginBottom: 12,
    position: 'relative',
  },
  photo: {
    width: '100%',
    height: '100%',
    borderRadius: 12,
  },
  removePhotoButton: {
    position: 'absolute',
    top: 8,
    right: 8,
    backgroundColor: 'rgba(0,0,0,0.6)',
    borderRadius: 12,
    width: 24,
    height: 24,
    justifyContent: 'center',
    alignItems: 'center',
  },
  addPhotoButton: {
    width: '48%',
    aspectRatio: 1,
    borderWidth: 2,
    borderColor: '#FF6B6B',
    borderStyle: 'dashed',
    borderRadius: 12,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 12,
  },
  addPhotoText: {
    color: '#FF6B6B',
    fontSize: 14,
    fontWeight: '500',
    marginTop: 8,
  },
  bioInput: {
    borderWidth: 1,
    borderColor: '#E0E0E0',
    borderRadius: 12,
    padding: 16,
    fontSize: 16,
    height: 120,
    backgroundColor: '#F8F8F8',
  },
  characterCount: {
    textAlign: 'right',
    color: '#666',
    fontSize: 12,
    marginTop: 8,
  },
  interestsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  interestChip: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    borderWidth: 1,
    borderColor: '#E0E0E0',
    marginRight: 8,
    marginBottom: 8,
    backgroundColor: '#fff',
  },
  interestChipSelected: {
    backgroundColor: '#FF6B6B',
    borderColor: '#FF6B6B',
  },
  interestText: {
    fontSize: 14,
    color: '#333',
  },
  interestTextSelected: {
    color: '#fff',
  },
  goalsList: {
    gap: 16,
  },
  goalOption: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    borderWidth: 1,
    borderColor: '#E0E0E0',
    borderRadius: 12,
    backgroundColor: '#F8F8F8',
  },
  goalOptionSelected: {
    borderColor: '#FF6B6B',
    backgroundColor: '#FFF5F5',
  },
  goalText: {
    fontSize: 16,
    color: '#333',
    marginLeft: 12,
  },
  goalTextSelected: {
    color: '#FF6B6B',
    fontWeight: '500',
  },
  footer: {
    padding: 20,
    borderTopWidth: 1,
    borderTopColor: '#E0E0E0',
  },
  nextButton: {
    backgroundColor: '#FF6B6B',
    borderRadius: 12,
    height: 50,
    justifyContent: 'center',
    alignItems: 'center',
  },
  nextButtonDisabled: {
    backgroundColor: '#FFB3B3',
  },
  nextButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
  },
});
