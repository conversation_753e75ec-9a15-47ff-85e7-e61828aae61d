/**
 * Firebase Service - Real Firebase Implementation
 * This service provides real Firebase integration for production use
 */

import {User, LoginCredentials, SignupData, UserProfile} from '../types/auth';
import {Message, Chat, Match} from '../types/chat';
import {generateUserId} from '../utils/helpers';
import {firebaseAuth, firebaseFirestore, firebaseStorage, COLLECTIONS, STORAGE_PATHS} from '../config/firebase';

// Real Firebase implementation
export class FirebaseService {
  // Authentication methods
  static async signUp(data: SignupData): Promise<User> {
    try {
      // Create user with Firebase Auth
      const userCredential = await firebaseAuth.createUserWithEmailAndPassword(
        data.email,
        data.password
      );

      const firebaseUser = userCredential.user;

      // Update display name
      await firebaseUser.updateProfile({
        displayName: `${data.firstName} ${data.lastName}`,
      });

      // Create user object
      const user: User = {
        id: firebaseUser.uid,
        email: data.email,
        firstName: data.firstName,
        lastName: data.lastName,
        dateOfBirth: data.dateOfBirth,
        phoneNumber: data.phoneNumber,
        profileComplete: false,
        subscriptionPlan: {
          id: 'free',
          name: 'free',
          price: 0,
          duration: 'monthly',
          features: ['Basic matching', 'Limited likes'],
          isActive: true,
        },
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      // Save user to Firestore
      await this.createUser(user);

      return user;
    } catch (error: any) {
      console.error('Firebase signup error:', error);
      throw new Error(this.getAuthErrorMessage(error.code));
    }
  }

  static async signIn(credentials: LoginCredentials): Promise<User> {
    try {
      // Sign in with Firebase Auth
      const userCredential = await firebaseAuth.signInWithEmailAndPassword(
        credentials.email,
        credentials.password
      );

      const firebaseUser = userCredential.user;

      // Get user data from Firestore
      const userData = await this.getUser(firebaseUser.uid);

      if (userData) {
        return userData;
      }

      // If user data doesn't exist in Firestore, create it
      const user: User = {
        id: firebaseUser.uid,
        email: firebaseUser.email || '',
        firstName: firebaseUser.displayName?.split(' ')[0] || '',
        lastName: firebaseUser.displayName?.split(' ')[1] || '',
        dateOfBirth: new Date('1990-01-01'), // Default date
        profileComplete: false,
        subscriptionPlan: {
          id: 'free',
          name: 'free',
          price: 0,
          duration: 'monthly',
          features: ['Basic matching', 'Limited likes'],
          isActive: true,
        },
        createdAt: new Date(firebaseUser.metadata.creationTime || Date.now()),
        updatedAt: new Date(),
      };

      await this.createUser(user);
      return user;
    } catch (error: any) {
      console.error('Firebase signin error:', error);
      throw new Error(this.getAuthErrorMessage(error.code));
    }
  }

  static async signOut(): Promise<void> {
    try {
      await firebaseAuth.signOut();
    } catch (error: any) {
      console.error('Firebase signout error:', error);
      throw new Error('Failed to sign out');
    }
  }

  // Database methods
  static async createUser(user: User): Promise<void> {
    try {
      await firebaseFirestore
        .collection(COLLECTIONS.USERS)
        .doc(user.id)
        .set(user, {merge: true});
    } catch (error) {
      console.error('Error creating user:', error);
      throw new Error('Failed to create user profile');
    }
  }

  static async getUser(userId: string): Promise<User | null> {
    try {
      const doc = await firebaseFirestore
        .collection(COLLECTIONS.USERS)
        .doc(userId)
        .get();

      if (doc.exists) {
        const data = doc.data();
        return {
          ...data,
          createdAt: data?.createdAt?.toDate() || new Date(),
          updatedAt: data?.updatedAt?.toDate() || new Date(),
          dateOfBirth: data?.dateOfBirth?.toDate() || new Date(),
        } as User;
      }
      return null;
    } catch (error) {
      console.error('Error getting user:', error);
      throw new Error('Failed to get user profile');
    }
  }

  static async updateUser(userId: string, updates: Partial<User>): Promise<void> {
    try {
      await firebaseFirestore
        .collection(COLLECTIONS.USERS)
        .doc(userId)
        .update({
          ...updates,
          updatedAt: new Date(),
        });
    } catch (error) {
      console.error('Error updating user:', error);
      throw new Error('Failed to update user profile');
    }
  }

  static async createProfile(profile: UserProfile): Promise<void> {
    try {
      await firebaseFirestore
        .collection(COLLECTIONS.PROFILES)
        .doc(profile.userId)
        .set(profile, {merge: true});
    } catch (error) {
      console.error('Error creating profile:', error);
      throw new Error('Failed to create profile');
    }
  }

  static async getProfile(userId: string): Promise<UserProfile | null> {
    try {
      const doc = await firebaseFirestore
        .collection(COLLECTIONS.PROFILES)
        .doc(userId)
        .get();

      if (doc.exists) {
        return doc.data() as UserProfile;
      }
      return null;
    } catch (error) {
      console.error('Error getting profile:', error);
      throw new Error('Failed to get profile');
    }
  }

  // Chat methods
  static async sendMessage(message: Omit<Message, 'id'>): Promise<string> {
    try {
      const messageRef = firebaseFirestore
        .collection(COLLECTIONS.CHATS)
        .doc(message.chatId)
        .collection(COLLECTIONS.MESSAGES)
        .doc();

      const messageWithId: Message = {
        ...message,
        id: messageRef.id,
      };

      await messageRef.set(messageWithId);

      // Update chat's last message
      await firebaseFirestore
        .collection(COLLECTIONS.CHATS)
        .doc(message.chatId)
        .update({
          lastMessage: messageWithId,
          lastActivity: new Date(),
        });

      return messageRef.id;
    } catch (error) {
      console.error('Error sending message:', error);
      throw new Error('Failed to send message');
    }
  }

  static async getMessages(chatId: string, limit: number = 50): Promise<Message[]> {
    try {
      const snapshot = await firebaseFirestore
        .collection(COLLECTIONS.CHATS)
        .doc(chatId)
        .collection(COLLECTIONS.MESSAGES)
        .orderBy('timestamp', 'desc')
        .limit(limit)
        .get();

      return snapshot.docs.map(doc => {
        const data = doc.data();
        return {
          ...data,
          timestamp: data.timestamp?.toDate() || new Date(),
        } as Message;
      }).reverse();
    } catch (error) {
      console.error('Error getting messages:', error);
      throw new Error('Failed to get messages');
    }
  }

  static async createChat(participants: string[]): Promise<string> {
    try {
      const chatRef = firebaseFirestore.collection(COLLECTIONS.CHATS).doc();
      const chat: Chat = {
        id: chatRef.id,
        participants,
        lastActivity: new Date(),
        isActive: true,
        unreadCount: 0,
        matchDate: new Date(),
      };

      await chatRef.set(chat);
      return chatRef.id;
    } catch (error) {
      console.error('Error creating chat:', error);
      throw new Error('Failed to create chat');
    }
  }

  // Storage methods
  static async uploadProfilePhoto(userId: string, imageUri: string): Promise<string> {
    try {
      const filename = `${userId}_${Date.now()}.jpg`;
      const reference = firebaseStorage.ref(`${STORAGE_PATHS.PROFILE_PHOTOS}/${userId}/${filename}`);

      await reference.putFile(imageUri);
      const downloadURL = await reference.getDownloadURL();
      return downloadURL;
    } catch (error) {
      console.error('Error uploading profile photo:', error);
      throw new Error('Failed to upload profile photo');
    }
  }

  static async uploadChatImage(chatId: string, imageUri: string): Promise<string> {
    try {
      const filename = `${Date.now()}.jpg`;
      const reference = firebaseStorage.ref(`${STORAGE_PATHS.CHAT_IMAGES}/${chatId}/${filename}`);

      await reference.putFile(imageUri);
      const downloadURL = await reference.getDownloadURL();
      return downloadURL;
    } catch (error) {
      console.error('Error uploading chat image:', error);
      throw new Error('Failed to upload chat image');
    }
  }

  static async deleteFile(downloadURL: string): Promise<void> {
    try {
      const reference = firebaseStorage.refFromURL(downloadURL);
      await reference.delete();
    } catch (error) {
      console.error('Error deleting file:', error);
      throw new Error('Failed to delete file');
    }
  }

  // Utility methods
  static isFirebaseAvailable(): boolean {
    try {
      return firebaseAuth !== null && firebaseFirestore !== null;
    } catch (error) {
      console.warn('Firebase not available:', error);
      return false;
    }
  }

  static async testConnection(): Promise<boolean> {
    try {
      // Test Firestore connection
      await firebaseFirestore.collection('test').limit(1).get();
      console.log('Firebase connection successful');
      return true;
    } catch (error) {
      console.error('Firebase connection test failed:', error);
      return false;
    }
  }

  // Real-time listeners
  static listenToMessages(
    chatId: string,
    callback: (messages: Message[]) => void,
    limit: number = 50
  ) {
    return firebaseFirestore
      .collection(COLLECTIONS.CHATS)
      .doc(chatId)
      .collection(COLLECTIONS.MESSAGES)
      .orderBy('timestamp', 'desc')
      .limit(limit)
      .onSnapshot(
        snapshot => {
          const messages = snapshot.docs.map(doc => {
            const data = doc.data();
            return {
              ...data,
              timestamp: data.timestamp?.toDate() || new Date(),
            } as Message;
          }).reverse();
          callback(messages);
        },
        error => {
          console.error('Error listening to messages:', error);
        }
      );
  }

  static onAuthStateChanged(callback: (user: any) => void) {
    return firebaseAuth.onAuthStateChanged(callback);
  }

  // Error handling
  private static getAuthErrorMessage(errorCode: string): string {
    switch (errorCode) {
      case 'auth/email-already-in-use':
        return 'This email is already registered. Please use a different email or sign in.';
      case 'auth/invalid-email':
        return 'Please enter a valid email address.';
      case 'auth/operation-not-allowed':
        return 'Email/password accounts are not enabled. Please contact support.';
      case 'auth/weak-password':
        return 'Password is too weak. Please choose a stronger password.';
      case 'auth/user-disabled':
        return 'This account has been disabled. Please contact support.';
      case 'auth/user-not-found':
        return 'No account found with this email. Please check your email or sign up.';
      case 'auth/wrong-password':
        return 'Incorrect password. Please try again.';
      case 'auth/invalid-credential':
        return 'Invalid email or password. Please try again.';
      case 'auth/too-many-requests':
        return 'Too many failed attempts. Please try again later.';
      case 'auth/network-request-failed':
        return 'Network error. Please check your connection and try again.';
      default:
        return 'An error occurred during authentication. Please try again.';
    }
  }
}

// Export individual service classes for when Firebase is properly set up
export class FirebaseAuthService extends FirebaseService {}
export class FirebaseFirestoreService extends FirebaseService {}
export class FirebaseStorageService extends FirebaseService {}
