/**
 * Firebase Service - Mock Implementation for Development
 * This service provides mock Firebase functionality while we resolve build issues
 */

import {User, LoginCredentials, SignupData, UserProfile} from '../types/auth';
import {Message, Chat, Match} from '../types/chat';
import {generateUserId} from '../utils/helpers';

// Temporarily using mock implementation to resolve build issues
// import {firebaseAuth, firebaseFirestore, firebaseStorage, COLLECTIONS, STORAGE_PATHS} from '../config/firebase';

// Mock Firebase implementation for development
export class FirebaseService {
  // Authentication methods
  static async signUp(data: SignupData): Promise<User> {
    // Mock implementation
    await new Promise(resolve => setTimeout(resolve, 1000)); // Simulate network delay

    const user: User = {
      id: generateUserId(),
      email: data.email,
      firstName: data.firstName,
      lastName: data.lastName,
      dateOfBirth: data.dateOfBirth,
      phoneNumber: data.phoneNumber,
      profileComplete: false,
      subscriptionPlan: {
        id: 'free',
        name: 'free',
        price: 0,
        duration: 'monthly',
        features: ['Basic matching', 'Limited likes'],
        isActive: true,
      },
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    console.log('Mock Firebase: User signed up:', user.id);
    return user;
  }

  static async signIn(credentials: LoginCredentials): Promise<User> {
    // Mock implementation
    await new Promise(resolve => setTimeout(resolve, 1000)); // Simulate network delay

    const user: User = {
      id: generateUserId(),
      email: credentials.email,
      firstName: 'John',
      lastName: 'Doe',
      dateOfBirth: new Date('1990-01-01'),
      profileComplete: false,
      subscriptionPlan: {
        id: 'free',
        name: 'free',
        price: 0,
        duration: 'monthly',
        features: ['Basic matching', 'Limited likes'],
        isActive: true,
      },
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    console.log('Mock Firebase: User signed in:', user.id);
    return user;
  }

  static async signOut(): Promise<void> {
    // Mock implementation
    console.log('Mock Firebase: User signed out');
    return Promise.resolve();
  }

  // Database methods
  static async createUser(user: User): Promise<void> {
    // Mock implementation
    console.log('Mock Firebase: Creating user:', user.id);
    return Promise.resolve();
  }

  static async getUser(userId: string): Promise<User | null> {
    // Mock implementation
    console.log('Mock Firebase: Getting user:', userId);
    return null;
  }

  static async updateUser(userId: string, updates: Partial<User>): Promise<void> {
    // Mock implementation
    console.log('Mock Firebase: Updating user:', userId, updates);
    return Promise.resolve();
  }

  static async createProfile(profile: UserProfile): Promise<void> {
    // Mock implementation
    console.log('Mock Firebase: Creating profile:', profile.userId);
    return Promise.resolve();
  }

  static async getProfile(userId: string): Promise<UserProfile | null> {
    // Mock implementation
    console.log('Mock Firebase: Getting profile:', userId);
    return null;
  }

  // Chat methods
  static async sendMessage(message: Omit<Message, 'id'>): Promise<string> {
    // Mock implementation
    const messageId = generateUserId();
    console.log('Mock Firebase: Sending message:', messageId);
    return messageId;
  }

  static async getMessages(chatId: string, limit: number = 50): Promise<Message[]> {
    // Mock implementation
    console.log('Mock Firebase: Getting messages for chat:', chatId);
    return [];
  }

  static async createChat(participants: string[]): Promise<string> {
    // Mock implementation
    const chatId = generateUserId();
    console.log('Mock Firebase: Creating chat:', chatId);
    return chatId;
  }

  // Storage methods
  static async uploadProfilePhoto(userId: string, imageUri: string): Promise<string> {
    // Mock implementation - return the original URI
    console.log('Mock Firebase: Uploading photo for user:', userId);
    return imageUri;
  }

  static async uploadChatImage(chatId: string, imageUri: string): Promise<string> {
    // Mock implementation - return the original URI
    console.log('Mock Firebase: Uploading chat image for chat:', chatId);
    return imageUri;
  }

  static async deleteFile(downloadURL: string): Promise<void> {
    // Mock implementation
    console.log('Mock Firebase: Deleting file:', downloadURL);
    return Promise.resolve();
  }

  // Utility methods
  static isFirebaseAvailable(): boolean {
    // Mock implementation
    console.log('Mock Firebase: Checking availability');
    return true;
  }

  static async testConnection(): Promise<boolean> {
    // Mock implementation
    console.log('Mock Firebase: Testing connection');
    return true;
  }

  // Real-time listeners
  static listenToMessages(
    chatId: string,
    callback: (messages: Message[]) => void,
    limit: number = 50
  ) {
    // Mock implementation - return empty unsubscribe function
    console.log('Mock Firebase: Listening to messages for chat:', chatId);
    return () => {};
  }

  static onAuthStateChanged(callback: (user: any) => void) {
    // Mock implementation - return empty unsubscribe function
    console.log('Mock Firebase: Setting up auth state listener');
    return () => {};
  }
}

// Export individual service classes for when Firebase is properly set up
export class FirebaseAuthService extends FirebaseService {}
export class FirebaseFirestoreService extends FirebaseService {}
export class FirebaseStorageService extends FirebaseService {}
