/**
 * Chat and Messaging Types
 */

export interface Message {
  id: string;
  chatId: string;
  senderId: string;
  receiverId: string;
  content: string;
  type: 'text' | 'image' | 'gif' | 'emoji';
  timestamp: Date;
  isRead: boolean;
  isDelivered: boolean;
  replyTo?: string; // ID of message being replied to
}

export interface Chat {
  id: string;
  participants: string[]; // User IDs
  lastMessage?: Message;
  lastActivity: Date;
  isActive: boolean;
  unreadCount: number;
  matchDate: Date;
}

export interface Match {
  id: string;
  userId: string;
  name: string;
  age: number;
  photos: string[];
  bio: string;
  distance: number;
  matchDate: Date;
  isOnline: boolean;
  lastSeen: Date;
  commonInterests: string[];
  chat?: Chat;
}

export interface ChatState {
  matches: Match[];
  activeChats: Chat[];
  messages: { [chatId: string]: Message[] };
  isLoading: boolean;
  error: string | null;
}

export interface ChatContextType {
  state: ChatState;
  sendMessage: (chatId: string, content: string, type?: Message['type']) => Promise<void>;
  markAsRead: (chatId: string, messageId: string) => Promise<void>;
  loadMessages: (chatId: string) => Promise<void>;
  createChat: (matchId: string) => Promise<string>;
  deleteChat: (chatId: string) => Promise<void>;
  blockUser: (userId: string) => Promise<void>;
  reportUser: (userId: string, reason: string) => Promise<void>;
}

export interface TypingIndicator {
  chatId: string;
  userId: string;
  isTyping: boolean;
  timestamp: Date;
}
