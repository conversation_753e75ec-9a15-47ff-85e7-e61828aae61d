/**
 * Authentication Context Provider
 */

import React, {createContext, useContext, useReducer, useEffect} from 'react';
import AsyncStorage from '@react-native-async-storage/async-storage';
import {
  AuthState,
  AuthContextType,
  LoginCredentials,
  SignupData,
  User,
  UserProfile,
} from '../types/auth';
import {FirebaseAuthService, FirebaseFirestoreService} from '../services/FirebaseService';
import {generateUserId} from '../utils/helpers';

// Initial state
const initialState: AuthState = {
  user: null,
  profile: null,
  isAuthenticated: false,
  isLoading: true,
  error: null,
};

// Action types
type AuthAction =
  | {type: 'SET_LOADING'; payload: boolean}
  | {type: 'SET_ERROR'; payload: string | null}
  | {type: 'LOGIN_SUCCESS'; payload: {user: User; profile: UserProfile | null}}
  | {type: 'LOGOUT'}
  | {type: 'UPDATE_PROFILE'; payload: UserProfile};

// Reducer
const authReducer = (state: AuthState, action: AuthAction): AuthState => {
  switch (action.type) {
    case 'SET_LOADING':
      return {...state, isLoading: action.payload};
    case 'SET_ERROR':
      return {...state, error: action.payload, isLoading: false};
    case 'LOGIN_SUCCESS':
      return {
        ...state,
        user: action.payload.user,
        profile: action.payload.profile,
        isAuthenticated: true,
        isLoading: false,
        error: null,
      };
    case 'LOGOUT':
      return {
        ...initialState,
        isLoading: false,
      };
    case 'UPDATE_PROFILE':
      return {
        ...state,
        profile: action.payload,
      };
    default:
      return state;
  }
};

// Create context
const AuthContext = createContext<AuthContextType | undefined>(undefined);

// Provider component
export const AuthProvider: React.FC<{children: React.ReactNode}> = ({
  children,
}) => {
  const [state, dispatch] = useReducer(authReducer, initialState);

  // Check for existing session on app start
  useEffect(() => {
    checkAuthState();
  }, []);

  const checkAuthState = async () => {
    try {
      const userToken = await AsyncStorage.getItem('userToken');
      const userData = await AsyncStorage.getItem('userData');
      const profileData = await AsyncStorage.getItem('profileData');

      if (userToken && userData) {
        const user = JSON.parse(userData);
        const profile = profileData ? JSON.parse(profileData) : null;
        dispatch({type: 'LOGIN_SUCCESS', payload: {user, profile}});
      } else {
        dispatch({type: 'SET_LOADING', payload: false});
      }
    } catch (error) {
      console.error('Error checking auth state:', error);
      dispatch({type: 'SET_LOADING', payload: false});
    }
  };

  const login = async (credentials: LoginCredentials) => {
    try {
      dispatch({type: 'SET_LOADING', payload: true});
      dispatch({type: 'SET_ERROR', payload: null});

      // Use Firebase authentication
      const user = await FirebaseAuthService.signIn(credentials);

      // Get user profile from Firestore
      const profile = await FirebaseFirestoreService.getProfile(user.id);

      // Store auth data locally
      await AsyncStorage.setItem('userToken', user.id);
      await AsyncStorage.setItem('userData', JSON.stringify(user));
      if (profile) {
        await AsyncStorage.setItem('profileData', JSON.stringify(profile));
      }

      dispatch({type: 'LOGIN_SUCCESS', payload: {user, profile}});
    } catch (error: any) {
      dispatch({type: 'SET_ERROR', payload: error.message || 'Login failed. Please try again.'});
    }
  };

  const signup = async (data: SignupData) => {
    try {
      dispatch({type: 'SET_LOADING', payload: true});
      dispatch({type: 'SET_ERROR', payload: null});

      // Use Firebase authentication
      const user = await FirebaseAuthService.signUp(data);

      // Create user document in Firestore
      await FirebaseFirestoreService.createUser(user);

      // Store auth data locally
      await AsyncStorage.setItem('userToken', user.id);
      await AsyncStorage.setItem('userData', JSON.stringify(user));

      dispatch({type: 'LOGIN_SUCCESS', payload: {user, profile: null}});
    } catch (error: any) {
      dispatch({type: 'SET_ERROR', payload: error.message || 'Signup failed. Please try again.'});
    }
  };

  const logout = async () => {
    try {
      // Sign out from Firebase
      await FirebaseAuthService.signOut();

      // Clear local storage
      await AsyncStorage.multiRemove(['userToken', 'userData', 'profileData']);
      dispatch({type: 'LOGOUT'});
    } catch (error) {
      console.error('Error during logout:', error);
    }
  };

  const updateProfile = async (profileData: Partial<UserProfile>) => {
    try {
      if (!state.user) return;

      const updatedProfile: UserProfile = {
        userId: state.user.id,
        bio: '',
        photos: [],
        interests: [],
        location: {
          city: '',
          state: '',
          country: '',
        },
        preferences: {
          ageRange: {min: 18, max: 35},
          maxDistance: 50,
          genderPreference: 'everyone',
        },
        relationshipGoals: 'unsure',
        ...state.profile,
        ...profileData,
      };

      // Save to Firestore
      await FirebaseFirestoreService.createProfile(updatedProfile);

      // Check if profile is now complete (matches ProfileSetupScreen requirements)
      const isProfileComplete = (updatedProfile.bio?.length || 0) >= 50 &&
                               (updatedProfile.photos?.length || 0) >= 2 &&
                               (updatedProfile.interests?.length || 0) >= 3 &&
                               updatedProfile.relationshipGoals !== 'unsure';

      console.log('Profile completion check:', {
        bioLength: updatedProfile.bio?.length || 0,
        photosCount: updatedProfile.photos?.length || 0,
        interestsCount: updatedProfile.interests?.length || 0,
        relationshipGoals: updatedProfile.relationshipGoals,
        isProfileComplete,
        currentUserProfileComplete: state.user.profileComplete
      });

      // Update user's profileComplete status if this is the first profile setup
      if (!state.user.profileComplete && isProfileComplete) {
        console.log('Updating user profile to complete');
        const updatedUser = {...state.user, profileComplete: true};
        await FirebaseFirestoreService.updateUser(state.user.id, {profileComplete: true});

        // Update user in local state
        dispatch({type: 'LOGIN_SUCCESS', payload: updatedUser});

        // Update local storage
        await AsyncStorage.setItem('userData', JSON.stringify(updatedUser));
      }

      // Update local storage for profile
      await AsyncStorage.setItem('profileData', JSON.stringify(updatedProfile));
      dispatch({type: 'UPDATE_PROFILE', payload: updatedProfile});
    } catch (error: any) {
      dispatch({type: 'SET_ERROR', payload: error.message || 'Failed to update profile.'});
    }
  };

  const upgradeSubscription = async (planId: string) => {
    try {
      // Mock subscription upgrade
      console.log('Upgrading to plan:', planId);
      // Implementation would handle payment processing
    } catch (error) {
      dispatch({type: 'SET_ERROR', payload: 'Subscription upgrade failed.'});
    }
  };

  const value: AuthContextType = {
    state,
    login,
    signup,
    logout,
    updateProfile,
    upgradeSubscription,
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
};

// Hook to use auth context
export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};
