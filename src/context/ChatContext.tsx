/**
 * Chat Context Provider
 */

import React, {createContext, useContext, useReducer, useEffect} from 'react';
import {
  ChatState,
  ChatContextType,
  Message,
  Chat,
  Match,
} from '../types/chat';
import {FirebaseFirestoreService} from '../services/FirebaseService';
import {generateUserId} from '../utils/helpers';
import {useAuth} from './AuthContext';

// Initial state
const initialState: ChatState = {
  matches: [],
  activeChats: [],
  messages: {},
  isLoading: false,
  error: null,
};

// Action types
type ChatAction =
  | {type: 'SET_LOADING'; payload: boolean}
  | {type: 'SET_ERROR'; payload: string | null}
  | {type: 'SET_MATCHES'; payload: Match[]}
  | {type: 'SET_CHATS'; payload: Chat[]}
  | {type: 'SET_MESSAGES'; payload: {chatId: string; messages: Message[]}}
  | {type: 'ADD_MESSAGE'; payload: Message}
  | {type: 'MARK_AS_READ'; payload: {chatId: string; messageId: string}}
  | {type: 'UPDATE_CHAT'; payload: Chat};

// Reducer
const chatReducer = (state: ChatState, action: ChatAction): ChatState => {
  switch (action.type) {
    case 'SET_LOADING':
      return {...state, isLoading: action.payload};
    case 'SET_ERROR':
      return {...state, error: action.payload, isLoading: false};
    case 'SET_MATCHES':
      return {...state, matches: action.payload};
    case 'SET_CHATS':
      return {...state, activeChats: action.payload};
    case 'SET_MESSAGES':
      return {
        ...state,
        messages: {
          ...state.messages,
          [action.payload.chatId]: action.payload.messages,
        },
      };
    case 'ADD_MESSAGE':
      const chatId = action.payload.chatId;
      return {
        ...state,
        messages: {
          ...state.messages,
          [chatId]: [...(state.messages[chatId] || []), action.payload],
        },
      };
    case 'MARK_AS_READ':
      const {chatId: readChatId, messageId} = action.payload;
      return {
        ...state,
        messages: {
          ...state.messages,
          [readChatId]: state.messages[readChatId]?.map(msg =>
            msg.id === messageId ? {...msg, isRead: true} : msg
          ) || [],
        },
      };
    case 'UPDATE_CHAT':
      return {
        ...state,
        activeChats: state.activeChats.map(chat =>
          chat.id === action.payload.id ? action.payload : chat
        ),
      };
    default:
      return state;
  }
};

// Create context
const ChatContext = createContext<ChatContextType | undefined>(undefined);

// Mock data for development
const MOCK_MATCHES: Match[] = [
  {
    id: '1',
    userId: 'user1',
    name: 'Emma',
    age: 25,
    photos: ['https://via.placeholder.com/300x400/FF6B6B/FFFFFF?text=Emma'],
    bio: 'Love hiking and coffee ☕',
    distance: 2.5,
    matchDate: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000),
    isOnline: true,
    lastSeen: new Date(),
    commonInterests: ['Hiking', 'Coffee', 'Travel'],
  },
  {
    id: '2',
    userId: 'user2',
    name: 'Sophie',
    age: 28,
    photos: ['https://via.placeholder.com/300x400/4ECDC4/FFFFFF?text=Sophie'],
    bio: 'Photographer and dog lover 📸🐕',
    distance: 5.2,
    matchDate: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000),
    isOnline: false,
    lastSeen: new Date(Date.now() - 30 * 60 * 1000),
    commonInterests: ['Photography', 'Animals', 'Art'],
  },
  {
    id: '3',
    userId: 'user3',
    name: 'Maya',
    age: 26,
    photos: ['https://via.placeholder.com/300x400/45B7D1/FFFFFF?text=Maya'],
    bio: 'Yoga instructor and foodie 🧘‍♀️🍜',
    distance: 8.1,
    matchDate: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000),
    isOnline: true,
    lastSeen: new Date(),
    commonInterests: ['Fitness', 'Food', 'Wellness'],
  },
];

// Provider component
export const ChatProvider: React.FC<{children: React.ReactNode}> = ({
  children,
}) => {
  const [state, dispatch] = useReducer(chatReducer, initialState);
  const {state: authState} = useAuth();

  // Load initial data
  useEffect(() => {
    if (authState.isAuthenticated) {
      loadMatches();
    }
  }, [authState.isAuthenticated]);

  const loadMatches = async () => {
    try {
      dispatch({type: 'SET_LOADING', payload: true});
      
      // Mock API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Create mock chats for matches
      const matchesWithChats = MOCK_MATCHES.map(match => {
        const chatId = `chat_${match.id}`;
        const chat: Chat = {
          id: chatId,
          participants: [authState.user?.id || '', match.userId],
          lastActivity: new Date(Date.now() - Math.random() * 24 * 60 * 60 * 1000),
          isActive: true,
          unreadCount: Math.floor(Math.random() * 5),
          matchDate: match.matchDate,
        };
        
        return {...match, chat};
      });

      dispatch({type: 'SET_MATCHES', payload: matchesWithChats});
      dispatch({type: 'SET_CHATS', payload: matchesWithChats.map(m => m.chat!).filter(Boolean)});
      
      // Load some mock messages
      matchesWithChats.forEach(match => {
        if (match.chat) {
          loadMockMessages(match.chat.id, match.name);
        }
      });
      
    } catch (error) {
      dispatch({type: 'SET_ERROR', payload: 'Failed to load matches'});
    } finally {
      dispatch({type: 'SET_LOADING', payload: false});
    }
  };

  const loadMockMessages = (chatId: string, matchName: string) => {
    const mockMessages: Message[] = [
      {
        id: generateUserId(),
        chatId,
        senderId: 'user1',
        receiverId: authState.user?.id || '',
        content: `Hey! Thanks for the match! 😊`,
        type: 'text',
        timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000),
        isRead: true,
        isDelivered: true,
      },
      {
        id: generateUserId(),
        chatId,
        senderId: authState.user?.id || '',
        receiverId: 'user1',
        content: `Hi ${matchName}! Nice to meet you! How's your day going?`,
        type: 'text',
        timestamp: new Date(Date.now() - 1 * 60 * 60 * 1000),
        isRead: false,
        isDelivered: true,
      },
      {
        id: generateUserId(),
        chatId,
        senderId: 'user1',
        receiverId: authState.user?.id || '',
        content: `It's going great! I saw you like hiking too. Have you been to any good trails lately?`,
        type: 'text',
        timestamp: new Date(Date.now() - 30 * 60 * 1000),
        isRead: false,
        isDelivered: true,
      },
    ];

    dispatch({type: 'SET_MESSAGES', payload: {chatId, messages: mockMessages}});
  };

  const sendMessage = async (chatId: string, content: string, type: Message['type'] = 'text') => {
    try {
      const chat = state.activeChats.find(c => c.id === chatId);
      if (!chat || !authState.user) return;

      const message = {
        chatId,
        senderId: authState.user.id,
        receiverId: chat.participants.find(p => p !== authState.user?.id) || '',
        content,
        type,
        timestamp: new Date(),
        isRead: false,
        isDelivered: true,
      };

      // Send message to Firebase
      const messageId = await FirebaseFirestoreService.sendMessage(message);

      // Add message to local state
      const messageWithId: Message = {
        ...message,
        id: messageId,
      };
      dispatch({type: 'ADD_MESSAGE', payload: messageWithId});

      // Update chat with last message
      const updatedChat: Chat = {
        ...chat,
        lastMessage: messageWithId,
        lastActivity: new Date(),
      };
      dispatch({type: 'UPDATE_CHAT', payload: updatedChat});

    } catch (error: any) {
      dispatch({type: 'SET_ERROR', payload: error.message || 'Failed to send message'});
    }
  };

  const markAsRead = async (chatId: string, messageId: string) => {
    dispatch({type: 'MARK_AS_READ', payload: {chatId, messageId}});
  };

  const loadMessages = async (chatId: string) => {
    // Messages are already loaded in mock data
    // In real app, this would fetch messages from API
  };

  const createChat = async (matchId: string): Promise<string> => {
    const match = state.matches.find(m => m.id === matchId);
    if (!match) throw new Error('Match not found');
    
    if (match.chat) return match.chat.id;
    
    const chatId = `chat_${matchId}`;
    const newChat: Chat = {
      id: chatId,
      participants: [authState.user?.id || '', match.userId],
      lastActivity: new Date(),
      isActive: true,
      unreadCount: 0,
      matchDate: match.matchDate,
    };
    
    dispatch({type: 'UPDATE_CHAT', payload: newChat});
    return chatId;
  };

  const deleteChat = async (chatId: string) => {
    // Implementation for deleting chat
    console.log('Delete chat:', chatId);
  };

  const blockUser = async (userId: string) => {
    // Implementation for blocking user
    console.log('Block user:', userId);
  };

  const reportUser = async (userId: string, reason: string) => {
    // Implementation for reporting user
    console.log('Report user:', userId, reason);
  };

  const value: ChatContextType = {
    state,
    sendMessage,
    markAsRead,
    loadMessages,
    createChat,
    deleteChat,
    blockUser,
    reportUser,
  };

  return <ChatContext.Provider value={value}>{children}</ChatContext.Provider>;
};

// Hook to use chat context
export const useChat = () => {
  const context = useContext(ChatContext);
  if (context === undefined) {
    throw new Error('useChat must be used within a ChatProvider');
  }
  return context;
};
