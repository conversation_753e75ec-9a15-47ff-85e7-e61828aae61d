/**
 * Utility helper functions for the Dating App
 */

/**
 * Calculate age from birth date
 */
export const calculateAge = (birthDate: Date): number => {
  const today = new Date();
  const birth = new Date(birthDate);
  let age = today.getFullYear() - birth.getFullYear();
  const monthDiff = today.getMonth() - birth.getMonth();
  
  if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birth.getDate())) {
    age--;
  }
  
  return age;
};

/**
 * Format distance for display
 */
export const formatDistance = (distanceInKm: number): string => {
  if (distanceInKm < 1) {
    return 'Less than 1 km away';
  } else if (distanceInKm < 10) {
    return `${distanceInKm.toFixed(1)} km away`;
  } else {
    return `${Math.round(distanceInKm)} km away`;
  }
};

/**
 * Validate email format
 */
export const isValidEmail = (email: string): boolean => {
  const emailRegex = /^[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$/;

  // Additional checks for consecutive dots
  if (email.includes('..')) {
    return false;
  }

  return emailRegex.test(email);
};

/**
 * Generate a random user ID
 */
export const generateUserId = (): string => {
  return Math.random().toString(36).substr(2, 9);
};

/**
 * Check if user is online (mock function)
 */
export const isUserOnline = (lastSeen: Date): boolean => {
  const now = new Date();
  const timeDiff = now.getTime() - lastSeen.getTime();
  const minutesDiff = timeDiff / (1000 * 60);
  
  return minutesDiff < 5; // Consider online if last seen within 5 minutes
};
