// Mock for @react-native-firebase/messaging

const mockMessaging = {
  requestPermission: jest.fn(() => Promise.resolve(1)), // AuthorizationStatus.AUTHORIZED
  getToken: jest.fn(() => Promise.resolve('mock-fcm-token')),
  onMessage: jest.fn(() => () => {}), // unsubscribe function
  onNotificationOpenedApp: jest.fn(() => () => {}), // unsubscribe function
  getInitialNotification: jest.fn(() => Promise.resolve(null)),
  subscribeToTopic: jest.fn(() => Promise.resolve()),
  unsubscribeFromTopic: jest.fn(() => Promise.resolve()),
};

export default () => mockMessaging;
