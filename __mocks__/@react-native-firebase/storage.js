// Mock for @react-native-firebase/storage

const mockReference = {
  putFile: jest.fn(() => Promise.resolve()),
  getDownloadURL: jest.fn(() => Promise.resolve('https://mock-download-url.com/image.jpg')),
  delete: jest.fn(() => Promise.resolve()),
  getMetadata: jest.fn(() => Promise.resolve({ size: 1024 })),
  listAll: jest.fn(() => Promise.resolve({ items: [] })),
};

const mockStorage = {
  ref: jest.fn(() => mockReference),
  refFromURL: jest.fn(() => mockReference),
};

export default () => mockStorage;
