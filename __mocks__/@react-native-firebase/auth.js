// Mock for @react-native-firebase/auth

const mockUser = {
  uid: 'mock-user-id',
  email: '<EMAIL>',
  displayName: 'Test User',
  metadata: {
    creationTime: new Date().toISOString(),
  },
  updateProfile: jest.fn(() => Promise.resolve()),
};

const mockAuth = {
  currentUser: mockUser,
  createUserWithEmailAndPassword: jest.fn(() => 
    Promise.resolve({ user: mockUser })
  ),
  signInWithEmailAndPassword: jest.fn(() => 
    Promise.resolve({ user: mockUser })
  ),
  signOut: jest.fn(() => Promise.resolve()),
  onAuthStateChanged: jest.fn((callback) => {
    callback(mockUser);
    return () => {}; // unsubscribe function
  }),
};

export default () => mockAuth;
