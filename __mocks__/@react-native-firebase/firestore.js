// Mock for @react-native-firebase/firestore

const mockDoc = {
  id: 'mock-doc-id',
  exists: true,
  data: () => ({
    id: 'mock-doc-id',
    name: 'Mock Document',
    createdAt: new Date(),
  }),
};

const mockCollection = {
  doc: jest.fn(() => ({
    set: jest.fn(() => Promise.resolve()),
    get: jest.fn(() => Promise.resolve(mockDoc)),
    update: jest.fn(() => Promise.resolve()),
    delete: jest.fn(() => Promise.resolve()),
    collection: jest.fn(() => mockCollection),
    onSnapshot: jest.fn((callback) => {
      callback({ docs: [mockDoc] });
      return () => {}; // unsubscribe function
    }),
  })),
  add: jest.fn(() => Promise.resolve({ id: 'mock-doc-id' })),
  get: jest.fn(() => Promise.resolve({ docs: [mockDoc] })),
  where: jest.fn(() => mockCollection),
  orderBy: jest.fn(() => mockCollection),
  limit: jest.fn(() => mockCollection),
  onSnapshot: jest.fn((callback) => {
    callback({ docs: [mockDoc] });
    return () => {}; // unsubscribe function
  }),
};

const mockFirestore = {
  collection: jest.fn(() => mockCollection),
  batch: jest.fn(() => ({
    set: jest.fn(),
    update: jest.fn(),
    delete: jest.fn(),
    commit: jest.fn(() => Promise.resolve()),
  })),
};

export default () => mockFirestore;
