/**
 * @format
 */

import {generateUserId} from '../../src/utils/helpers';
import {Message} from '../../src/types/chat';

describe('Chat Functionality', () => {
  it('generates unique message IDs', () => {
    const id1 = generateUserId();
    const id2 = generateUserId();
    expect(id1).not.toBe(id2);
    expect(typeof id1).toBe('string');
    expect(typeof id2).toBe('string');
  });

  it('creates valid message objects', () => {
    const message: Message = {
      id: generateUserId(),
      chatId: 'test-chat',
      senderId: 'user1',
      receiverId: 'user2',
      content: 'Hello, how are you?',
      type: 'text',
      timestamp: new Date(),
      isRead: false,
      isDelivered: true,
    };

    expect(message.id).toBeTruthy();
    expect(message.content).toBe('Hello, how are you?');
    expect(message.type).toBe('text');
    expect(message.isRead).toBe(false);
    expect(message.isDelivered).toBe(true);
  });

  it('validates message types', () => {
    const validTypes: Message['type'][] = ['text', 'image', 'gif', 'emoji'];

    validTypes.forEach(type => {
      const message: Message = {
        id: generateUserId(),
        chatId: 'test-chat',
        senderId: 'user1',
        receiverId: 'user2',
        content: 'Test content',
        type,
        timestamp: new Date(),
        isRead: false,
        isDelivered: true,
      };

      expect(message.type).toBe(type);
    });
  });

  it('handles message timestamps correctly', () => {
    const now = new Date();
    const message: Message = {
      id: generateUserId(),
      chatId: 'test-chat',
      senderId: 'user1',
      receiverId: 'user2',
      content: 'Test message',
      type: 'text',
      timestamp: now,
      isRead: false,
      isDelivered: true,
    };

    expect(message.timestamp).toBeInstanceOf(Date);
    expect(message.timestamp.getTime()).toBeLessThanOrEqual(Date.now());
  });
});
