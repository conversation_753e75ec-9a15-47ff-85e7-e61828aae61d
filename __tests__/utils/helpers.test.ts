/**
 * @format
 */

import {
  calculateAge,
  formatDistance,
  isValidEmail,
  generateUserId,
  isUserOnline,
} from '../../src/utils/helpers';

describe('Helper Functions', () => {
  describe('calculateAge', () => {
    it('calculates age correctly for a birthday that has passed this year', () => {
      const birthDate = new Date('1990-01-01');
      const age = calculateAge(birthDate);
      const expectedAge = new Date().getFullYear() - 1990;
      expect(age).toBe(expectedAge);
    });

    it('calculates age correctly for a birthday that has not passed this year', () => {
      const nextYear = new Date().getFullYear() + 1;
      const birthDate = new Date(`1990-12-31`);
      const age = calculateAge(birthDate);
      const expectedAge = new Date().getFullYear() - 1990;
      expect(age).toBeLessThanOrEqual(expectedAge);
    });
  });

  describe('formatDistance', () => {
    it('formats distance less than 1 km correctly', () => {
      expect(formatDistance(0.5)).toBe('Less than 1 km away');
    });

    it('formats distance between 1-10 km with decimal', () => {
      expect(formatDistance(5.7)).toBe('5.7 km away');
    });

    it('formats distance over 10 km as rounded number', () => {
      expect(formatDistance(15.7)).toBe('16 km away');
    });
  });

  describe('isValidEmail', () => {
    it('returns true for valid email addresses', () => {
      expect(isValidEmail('<EMAIL>')).toBe(true);
      expect(isValidEmail('<EMAIL>')).toBe(true);
      expect(isValidEmail('<EMAIL>')).toBe(true);
    });

    it('returns false for invalid email addresses', () => {
      expect(isValidEmail('invalid-email')).toBe(false);
      expect(isValidEmail('test@')).toBe(false);
      expect(isValidEmail('@example.com')).toBe(false);
      expect(isValidEmail('<EMAIL>')).toBe(false);
    });
  });

  describe('generateUserId', () => {
    it('generates a string ID', () => {
      const id = generateUserId();
      expect(typeof id).toBe('string');
      expect(id.length).toBeGreaterThan(0);
    });

    it('generates unique IDs', () => {
      const id1 = generateUserId();
      const id2 = generateUserId();
      expect(id1).not.toBe(id2);
    });
  });

  describe('isUserOnline', () => {
    it('returns true for recent activity', () => {
      const recentTime = new Date(Date.now() - 2 * 60 * 1000); // 2 minutes ago
      expect(isUserOnline(recentTime)).toBe(true);
    });

    it('returns false for old activity', () => {
      const oldTime = new Date(Date.now() - 10 * 60 * 1000); // 10 minutes ago
      expect(isUserOnline(oldTime)).toBe(false);
    });
  });
});
