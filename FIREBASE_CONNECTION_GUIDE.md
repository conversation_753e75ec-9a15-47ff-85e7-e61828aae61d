# 🔥 Firebase Connection Guide - Dating App

## 🎯 **Current Status**
✅ **Firebase SDK Installed** - All Firebase packages ready  
✅ **Real Firebase Services** - Production-ready implementation  
✅ **Mock Configuration** - App builds and tests pass  
✅ **iOS Configuration** - Pods installed and configured  
⏳ **Firebase Project** - Needs to be created and connected  

## 🚀 **Step-by-Step Connection Process**

### **Step 1: Create Firebase Project**
1. **Open Firebase Console**: [https://console.firebase.google.com/](https://console.firebase.google.com/)
2. **Click "Create a project"**
3. **Project Name**: `dating-app-[your-name]` (e.g., `dating-app-alex`)
4. **Enable Google Analytics**: ✅ Recommended
5. **Choose Analytics Account**: Default or create new
6. **Click "Create project"**

### **Step 2: Add iOS App**
1. **In Firebase Console**, click **"Add app"** → **iOS**
2. **iOS bundle ID**: `org.reactjs.native.example.DatingApp`
3. **App nickname**: `Dating App iOS`
4. **App Store ID**: Leave blank for now
5. **Click "Register app"**

### **Step 3: Download Configuration**
1. **Download `GoogleService-Info.plist`**
2. **Replace the temporary file** at:
   ```
   ios/DatingApp/GoogleService-Info.plist
   ```
3. **In Xcode**:
   - Open `DatingApp.xcworkspace`
   - Right-click `DatingApp` folder
   - Select "Add Files to DatingApp"
   - Choose the downloaded `GoogleService-Info.plist`
   - ✅ Make sure "Add to target" is checked for DatingApp

### **Step 4: Enable Firebase Services**

#### **Authentication**
1. Go to **Authentication** → **Sign-in method**
2. Enable **Email/Password**
3. Optional: Enable **Google**, **Apple**, **Phone**

#### **Firestore Database**
1. Go to **Firestore Database**
2. Click **"Create database"**
3. Choose **"Start in test mode"** (for development)
4. Select **location** (choose closest to your users)

#### **Storage**
1. Go to **Storage**
2. Click **"Get started"**
3. Choose **"Start in test mode"**
4. Select **same location** as Firestore

#### **Cloud Messaging**
1. Go to **Cloud Messaging**
2. No additional setup needed initially

### **Step 5: Deploy Security Rules**

#### **Firestore Rules**
Copy this to Firestore → Rules:
```javascript
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Users can read/write their own data
    match /users/{userId} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
    }
    
    // Profiles are readable by authenticated users
    match /profiles/{userId} {
      allow read: if request.auth != null;
      allow write: if request.auth != null && request.auth.uid == userId;
    }
    
    // Chats are readable by participants
    match /chats/{chatId} {
      allow read, write: if request.auth != null && 
        request.auth.uid in resource.data.participants;
    }
    
    // Messages are readable by chat participants
    match /chats/{chatId}/messages/{messageId} {
      allow read, write: if request.auth != null && 
        request.auth.uid in get(/databases/$(database)/documents/chats/$(chatId)).data.participants;
    }
  }
}
```

#### **Storage Rules**
Copy this to Storage → Rules:
```javascript
rules_version = '2';
service firebase.storage {
  match /b/{bucket}/o {
    // Users can upload to their own folder
    match /users/{userId}/{allPaths=**} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
    }
    
    // Profile photos are publicly readable
    match /profile-photos/{allPaths=**} {
      allow read: if true;
      allow write: if request.auth != null;
    }
    
    // Chat images are readable by authenticated users
    match /chat-images/{allPaths=**} {
      allow read, write: if request.auth != null;
    }
  }
}
```

## 🧪 **Testing the Connection**

### **Check Configuration**
```bash
npm run check-firebase
```

### **Run Tests**
```bash
npm test
```

### **Build and Run**
```bash
npx react-native run-ios
```

## 🔧 **Troubleshooting**

### **Common Issues**

#### **Build Errors**
- Ensure `GoogleService-Info.plist` is added to Xcode target
- Clean build: `cd ios && rm -rf build && cd ..`
- Reinstall pods: `cd ios && rm -rf Pods Podfile.lock && pod install`

#### **Authentication Errors**
- Check bundle ID matches Firebase configuration
- Verify Firebase project is active
- Check internet connection

#### **Database Errors**
- Verify Firestore is enabled
- Check security rules allow your operations
- Ensure user is authenticated

## 🎉 **Success Indicators**

When everything is working, you should see:
- ✅ App builds without errors
- ✅ User can sign up/login
- ✅ Profile data saves to Firestore
- ✅ Chat messages sync in real-time
- ✅ Photos upload to Storage

## 📱 **Next Steps After Connection**

1. **Test Authentication**: Create account and login
2. **Test Profile Setup**: Complete profile with photos
3. **Test Chat System**: Send messages between users
4. **Enable Push Notifications**: Set up FCM
5. **Deploy to TestFlight**: Prepare for app store

## 🆘 **Need Help?**

Run the setup checker:
```bash
npm run check-firebase
```

Check Firebase status in app:
```javascript
import { FirebaseService } from './src/services/FirebaseService';
FirebaseService.testConnection();
```
