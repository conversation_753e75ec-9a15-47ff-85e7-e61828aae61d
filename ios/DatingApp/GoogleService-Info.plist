<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>API_KEY</key>
	<string>TEMP_API_KEY_REPLACE_WITH_REAL</string>
	<key>GCM_SENDER_ID</key>
	<string>123456789</string>
	<key>PLIST_VERSION</key>
	<string>1</string>
	<key>BUNDLE_ID</key>
	<string>org.reactjs.native.example.DatingApp</string>
	<key>PROJECT_ID</key>
	<string>dating-app-temp</string>
	<key>STORAGE_BUCKET</key>
	<string>dating-app-temp.appspot.com</string>
	<key>IS_ADS_ENABLED</key>
	<false></false>
	<key>IS_ANALYTICS_ENABLED</key>
	<false></false>
	<key>IS_APPINVITE_ENABLED</key>
	<true></true>
	<key>IS_GCM_ENABLED</key>
	<true></true>
	<key>IS_SIGNIN_ENABLED</key>
	<true></true>
	<key>GOOGLE_APP_ID</key>
	<string>1:123456789:ios:abcdef123456</string>
	<key>DATABASE_URL</key>
	<string>https://dating-app-temp-default-rtdb.firebaseio.com</string>
</dict>
</plist>

<!-- 
TEMPORARY FILE FOR DEVELOPMENT
This is a temporary configuration file to allow the app to build.
Replace this with the real GoogleService-Info.plist from Firebase Console.

IMPORTANT: 
- This file contains dummy values and won't work with real Firebase
- Download the actual file from Firebase Console
- Replace this file with the downloaded one
- Add the file to Xcode project target
-->
