<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>API_KEY</key>
	<string>YOUR_API_KEY_HERE</string>
	<key>GCM_SENDER_ID</key>
	<string>YOUR_SENDER_ID_HERE</string>
	<key>PLIST_VERSION</key>
	<string>1</string>
	<key>BUNDLE_ID</key>
	<string>org.reactjs.native.example.DatingApp</string>
	<key>PROJECT_ID</key>
	<string>YOUR_PROJECT_ID_HERE</string>
	<key>STORAGE_BUCKET</key>
	<string>YOUR_PROJECT_ID_HERE.appspot.com</string>
	<key>IS_ADS_ENABLED</key>
	<false></false>
	<key>IS_ANALYTICS_ENABLED</key>
	<false></false>
	<key>IS_APPINVITE_ENABLED</key>
	<true></true>
	<key>IS_GCM_ENABLED</key>
	<true></true>
	<key>IS_SIGNIN_ENABLED</key>
	<true></true>
	<key>GOOGLE_APP_ID</key>
	<string>YOUR_GOOGLE_APP_ID_HERE</string>
	<key>DATABASE_URL</key>
	<string>https://YOUR_PROJECT_ID_HERE-default-rtdb.firebaseio.com</string>
</dict>
</plist>

<!-- 
INSTRUCTIONS:
1. Go to Firebase Console (https://console.firebase.google.com/)
2. Create a new project or select existing project
3. Add iOS app with bundle ID: org.reactjs.native.example.DatingApp
4. Download the actual GoogleService-Info.plist file
5. Replace this template file with the downloaded file
6. Make sure the file is added to the Xcode project target

IMPORTANT: 
- Never commit the actual GoogleService-Info.plist to version control
- Add GoogleService-Info.plist to your .gitignore file
- This template is for reference only
-->
